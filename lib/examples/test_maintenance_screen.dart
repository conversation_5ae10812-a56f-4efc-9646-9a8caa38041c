import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import '../widgets/maintenance_screen.dart';

/// مثال لاختبار شاشة الصيانة
class TestMaintenanceScreen extends StatelessWidget {
  const TestMaintenanceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Test Maintenance Screen',
      debugShowCheckedModeBanner: false,
      localizationsDelegates: context.localizationDelegates,
      supportedLocales: context.supportedLocales,
      locale: context.locale,
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Test Maintenance'),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const MaintenanceScreen(
                        message: 'التطبيق قيد التحديث حالياً. يرجى المحاولة مرة أخرى لاحقاً.',
                        appName: 'busaty-parents',
                      ),
                    ),
                  );
                },
                child: const Text('عرض شاشة الصيانة'),
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => MaintenanceScreen(
                        message: 'التطبيق قيد التحديث حالياً. يرجى المحاولة مرة أخرى لاحقاً.',
                        appName: 'busaty-parents',
                        onRetry: () {
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('تم الضغط على إعادة المحاولة'),
                            ),
                          );
                        },
                      ),
                    ),
                  );
                },
                child: const Text('عرض شاشة الصيانة مع إعادة المحاولة'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// مثال لاختبار النظام بالكامل
class TestMaintenanceSystem extends StatefulWidget {
  const TestMaintenanceSystem({super.key});

  @override
  State<TestMaintenanceSystem> createState() => _TestMaintenanceSystemState();
}

class _TestMaintenanceSystemState extends State<TestMaintenanceSystem> {
  bool _isUnderMaintenance = false;

  @override
  Widget build(BuildContext context) {
    if (_isUnderMaintenance) {
      return MaterialApp(
        title: 'Test Maintenance System',
        debugShowCheckedModeBanner: false,
        localizationsDelegates: context.localizationDelegates,
        supportedLocales: context.supportedLocales,
        locale: context.locale,
        home: MaintenanceScreen(
          message: 'التطبيق قيد التحديث حالياً. يرجى المحاولة مرة أخرى لاحقاً.',
          appName: 'busaty-parents',
          onRetry: () {
            setState(() {
              _isUnderMaintenance = false;
            });
          },
        ),
      );
    }

    return MaterialApp(
      title: 'Test Maintenance System',
      debugShowCheckedModeBanner: false,
      localizationsDelegates: context.localizationDelegates,
      supportedLocales: context.supportedLocales,
      locale: context.locale,
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Test Maintenance System'),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'حالة التطبيق: ${_isUnderMaintenance ? "تحت الصيانة" : "يعمل بشكل طبيعي"}',
                style: const TextStyle(fontSize: 18),
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _isUnderMaintenance = true;
                  });
                },
                child: const Text('تفعيل وضع الصيانة'),
              ),
              const SizedBox(height: 10),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _isUnderMaintenance = false;
                  });
                },
                child: const Text('إلغاء وضع الصيانة'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
