# نظام صفحة الصيانة - دليل الاستخدام

## نظرة عامة
تم نسخ نظام صفحة الصيانة من مشروع "supervisor copy 2" إلى التطبيق الحالي مع تعديل الـ base URL ليتوافق مع تطبيق أولياء الأمور.

## الملفات المضافة

### 1. النماذج (Models)
- `lib/data/models/app_update_status_model.dart` - نموذج البيانات للاستجابة

### 2. المستودعات (Repositories)
- `lib/data/repo/app_update_status_repo.dart` - طبقة الوصول للبيانات

### 3. الخدمات (Services)
- `lib/services/app_update_service.dart` - منطق الأعمال الرئيسي

### 4. المساعدات (Utilities)
- `lib/utils/app_startup_helper.dart` - مساعدات بدء التطبيق

### 5. الواجهات (Widgets)
- `lib/widgets/maintenance_screen.dart` - شاشة الصيانة

### 6. التكوين (Configuration)
- `lib/config/app_update_config.dart` - إعدادات النظام

## التكوين

### Base URL المستخدم
- **التطبيق الحالي**: `https://test.busatyapp.com/api/parents/`
- **API Endpoint**: `app/updating`
- **URL الكامل**: `https://test.busatyapp.com/api/parents/app/updating`

### اسم التطبيق
- **اسم التطبيق**: `busaty-parents`
- **نوع التطبيق**: `parents`

## كيفية العمل

### 1. فحص تلقائي عند بدء التطبيق
يتم فحص حالة الصيانة تلقائياً في `main.dart` عند بدء التطبيق.

### 2. عرض شاشة الصيانة
إذا كان التطبيق تحت الصيانة، يتم عرض شاشة الصيانة بدلاً من الشاشة الرئيسية.

### 3. إعادة المحاولة
يمكن للمستخدم الضغط على زر "إعادة المحاولة" للتحقق من حالة الصيانة مرة أخرى.

## الاستخدام المتقدم

### فحص حالة الصيانة يدوياً
```dart
final appUpdateService = AppUpdateService();
final result = await appUpdateService.checkCurrentAppStatus(
  appName: 'busaty-parents',
  appType: AppType.parents,
);

if (result.isSuccess && result.isUpdating) {
  // التطبيق تحت الصيانة
}
```

### عرض حوار الصيانة
```dart
await AppStartupHelper.showMaintenanceDialogIfNeeded(
  context: context,
  appName: 'busaty-parents',
);
```

### استخدام AppStatusWrapper
```dart
AppStatusWrapper(
  appName: 'busaty-parents',
  appType: AppType.parents,
  child: YourWidget(),
)
```

## الترجمات المضافة

### العربية (ar.json)
- `maintenance_title`: "تحت الصيانة"
- `maintenance_message`: "التطبيق قيد التحديث حالياً. يرجى المحاولة مرة أخرى لاحقاً."
- `maintenance_checking`: "جاري التحقق..."
- `maintenance_retry`: "إعادة المحاولة"

### الإنجليزية (en.json)
- `maintenance_title`: "Under Maintenance"
- `maintenance_message`: "The app is currently being updated. Please try again later."
- `maintenance_checking`: "Checking..."
- `maintenance_retry`: "Retry"

## ملاحظات مهمة

1. **لا يتطلب مصادقة**: API endpoint لا يتطلب token
2. **معالجة الأخطاء**: في حالة فشل API، يتم عرض التطبيق العادي
3. **التخزين المؤقت**: يمكن تفعيل التخزين المؤقت من خلال `AppUpdateConfig`
4. **السجلات**: يتم تسجيل جميع العمليات لسهولة التتبع
5. **Directionality**: تم حل مشكلة عدم وجود Directionality widget بتغليف شاشة الصيانة بـ MaterialApp

## التحديثات المطلوبة في config_base.dart

تم إضافة:
```dart
/// App Update Status
static const String appUpdating = "app/updating";
```

## الاختبار

لاختبار النظام:
1. تأكد من أن الخادم يرجع `is_updating: true` في الاستجابة
2. ستظهر شاشة الصيانة تلقائياً
3. يمكن اختبار زر "إعادة المحاولة"
4. يمكن استخدام `lib/examples/test_maintenance_screen.dart` لاختبار الشاشة

## الإصلاحات المطبقة

### مشكلة Directionality Widget
تم حل مشكلة `No Directionality widget found` بتغليف شاشة الصيانة بـ MaterialApp في:
- `AppStartupHelper.checkAppStatusAndGetWidget()`
- `AppStatusWrapper.build()`
- `MyApp.build()` عند عرض شاشة التحميل

هذا يضمن وجود Directionality widget قبل عرض Scaffold.
