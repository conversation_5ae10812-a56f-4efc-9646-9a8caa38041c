// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    // if (kIsWeb) {
    //   return web;
    // }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  // static const FirebaseOptions web = FirebaseOptions(
  //   apiKey: 'AIzaSyAsbepfeItKyWtyJQG9maPA9B4UgdAtTSQ',
  //   appId: '1:66220404803:web:7f65593649a98ba46124f6',
  //   messagingSenderId: '66220404803',
  //   projectId: 'busaty-app',
  //   authDomain: 'busaty-app.firebaseapp.com',
  //   storageBucket: 'busaty-app.appspot.com',
  //   measurementId: 'G-T869W7J5H3',
  // );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDmpwqnRpySPiDVht0Rg6sPh1ZP3cBF8fc',
    appId: '1:545165014521:android:2a7d2c0b15e572f87db928',
    messagingSenderId: '545165014521',
    projectId: 'test-5c820',
    storageBucket: 'test-5c820.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDmpwqnRpySPiDVht0Rg6sPh1ZP3cBF8fc',
    appId: '1:545165014521:android:2a7d2c0b15e572f87db928',
    messagingSenderId: '545165014521',
    projectId: 'test-5c820',
    storageBucket: 'test-5c820.firebasestorage.app',
    iosBundleId: 'com.busaty.supervisor',
  );
}
