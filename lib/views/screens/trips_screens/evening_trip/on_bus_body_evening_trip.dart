import 'package:bus_driver/bloc/cubit/morning_trip_cubit/morning_trip_cubit.dart';
import 'package:bus_driver/config/global_variable.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../bloc/cubit/evening_trip_cubit/evening_trip_cubit.dart';
import '../../../../config/theme_colors.dart';
import '../../../../translations/local_keys.g.dart';
import '../../../../widgets/custom_container_dialog_w.dart';
import '../../../custom_widgets/build_table_row_widget.dart';
import '../../../custom_widgets/custom_text.dart';
import '../../student_address_screen/student_address_screen.dart';

class OnBusBodyEveningTrip extends StatelessWidget {
  const OnBusBodyEveningTrip({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<EveningTripCubit, EveningTripState>(
      builder: (context, state) {
        if (state is StartEveningTripLoadingState ||
            state is StartEveningTripSuccessState) {
          return const Center(
            child: CircularProgressIndicator(
              color: TColor.mainColor,
            ),
          );
        } else if (state is EveningTripInitial ||
            state is EveningTripStatusErrorState ||
            state is StartEveningTripErrorState ||
            state is EndEveningTripSuccessState) {
          return SizedBox(
            width: 300.w,
            height: 300.w,
            child: Center(
              child: CustomText(
                text: AppStrings.tripClosedStartTrip.tr(),
                fontSize: 17,
                fontW: FontWeight.w600,
              ),
            ),
          );
        } else {
          print(
              '---------------------------------------------------${EveningTripCubit.get(context).onBusEveningTripModel?.presentOnBus?.length}');
          if (EveningTripCubit.get(context)
                  .onBusEveningTripModel
                  ?.presentOnBus
                  ?.isEmpty ??
              true) {
            return SizedBox(
              width: 300.w,
              height: 300.w,
              child: Center(
                child: CustomText(
                  text: AppStrings.studentsNotFound.tr(),
                  fontSize: 17,
                  fontW: FontWeight.w600,
                ),
              ),
            );
          } else {
            debugPrint(
                "students on bus:------------------------------------------------------- ${EveningTripCubit.get(context).onBusEveningTripModel?.presentOnBus?.length}");
            return SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8.0),
                  clipBehavior: Clip.antiAlias,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15.0),
                  ),
                  child: Table(
                    columnWidths: const {
                      0: FlexColumnWidth(2),
                      1: FlexColumnWidth(1),
                      2: FlexColumnWidth(1),
                      3: FlexColumnWidth(1),
                      4: FlexColumnWidth(1),
                    },
                    border: TableBorder.all(
                        color: TColor.tabColors,
                        borderRadius: BorderRadius.circular(15.0)),
                    children: [
                      BuildTableRowWidget(
                        cell: [
                          (AppStrings.name.tr()),
                          (AppStrings.address.tr()),
                          (AppStrings.studentArrivedHome.tr()),
                          (AppStrings.absent.tr()),
                          (AppStrings.notification.tr()),
                        ],
                        header: true,
                      ).build(context),
                      ...List.generate(
                          EveningTripCubit.get(context)
                              .onBusEveningTripModel!
                              .presentOnBus!
                              .length, (index) {
                        final newStudent = EveningTripCubit.get(context)
                            .onBusEveningTripModel
                            ?.presentOnBus?[index];
                        return BuildTableRowWidget(
                          isFirstIcon: true,
                          isSecondIcon: true,
                          is2Icon: true,
                          isTabDown: true,
                          cell: [
                            // Custom widget for student name with location icon
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Flexible(
                                  child: Text(
                                    newStudent?.students?.name ?? '--',
                                    style: TextStyle(
                                      color: TColor.tabColors,
                                      fontSize: 15,
                                      fontWeight: FontWeight.w400,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                if (newStudent?.students?.hasCurrentAddress ==
                                    true) ...[
                                  const SizedBox(width: 4),
                                  Icon(
                                    Icons.location_on,
                                    size: 16,
                                    color: Colors.green,
                                  ),
                                ],
                              ],
                            ),
                            Icons.location_on,
                            Icons.home,
                            Icons.highlight_remove_outlined,
                            Icons.notifications_active,
                          ],
                          color: newStudent!.students!.myParents!.isEmpty
                              ? TColor.darkRed
                              : null,
                          onTapFirstCell: () async {
                            /// Address
                            if (newStudent.students?.latitude == null ||
                                newStudent.students?.longitude == null) {
                              Navigator.of(context).pushNamed(
                                  StudentAddressScreen.routeName,
                                  arguments: [
                                    newStudent.students?.latitude,
                                    newStudent.students?.longitude
                                  ]);
                            } else {
                              final Uri url = Uri.parse(
                                  "google.navigation:q=${newStudent.students?.latitude},${newStudent.students?.longitude}&mode=d");
                              if (!await launchUrl(url)) {
                                throw Exception('Could not launch $url');
                              }
                            }
                          },
                          onTapSecondCell: () async {
                            /// Arrived Home
                            EveningTripCubit.get(context)
                                .arrivedStudentEveningTrip(
                                  tripId: tripId,
                                  studentId: newStudent.studentId.toString(),
                                )
                                .then((value) => EveningTripCubit.get(context)
                                    .getOnBusEveningTrip(
                                        tripId: newStudent.tripId!));
                            // await NotificationsCubit.get(context).getParentFcmToken(studentId: newStudent.students?.id);
                            // if (context.mounted) {
                            //   if (NotificationsCubit.get(context).parentFcmToken?.data?.isNotEmpty == true) {
                            //     NotificationsRepo().sendNotification(
                            //       deviceTokens: NotificationsCubit.get(context).parentFcmToken!.data!,
                            //       title: 'مساء الخير',
                            //       body: 'الطالب ${newStudent.students?.name} وصل المنزل',
                            //     );
                            //   }
                            // }
                          },
                          onTapBeforeLastCell: () async {
                            EveningTripCubit.get(context)
                                .studentAbsentEveningTrip(
                              studentId: newStudent.studentId.toString(),
                            )
                                .then((value) {
                              EveningTripCubit.get(context).getOnBusEveningTrip(
                                  tripId: newStudent.tripId!);
                              EveningTripCubit.get(context)
                                  .getAbsentEveningTrip();
                            });
                            print(
                                '--------------------------------------asdsasadsada');
                            // await NotificationsCubit.get(context).getParentFcmToken(studentId: newStudent.students?.id);
                            // if (context.mounted) {
                            //   if (NotificationsCubit.get(context).parentFcmToken?.data?.isNotEmpty == true) {
                            //     NotificationsRepo().sendNotification(
                            //       deviceTokens: NotificationsCubit.get(context).parentFcmToken!.data!,
                            //       title: 'مساء الخير',
                            //       body: 'الطالب ${newStudent.students?.name} غائب',
                            //     );
                            //   }
                            // }
                          },
                          onTapDown: (TapDownDetails details) {
                            /// Notify
                            // NotificationsCubit.get(context).getParentFcmToken(studentId: newStudent.students?.id);
                            final RenderBox overlay = Overlay.of(context)
                                .context
                                .findRenderObject() as RenderBox;
                            final Offset offset =
                                overlay.localToGlobal(Offset.zero);
                            (MorningTripCubit.get()
                                        .groupAndSingleMessageModel !=
                                    null)
                                ? showMenu(
                                    shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(15.sp))),
                                    context: context,
                                    position: RelativeRect.fromLTRB(
                                      context.locale.toString() == "ar"
                                          ? offset.dx - 100
                                          : offset.dx + 100,
                                      details.globalPosition.dy + 15,
                                      0,
                                      0,
                                    ),
                                    items: (MorningTripCubit.get()
                                            .groupAndSingleMessageModel!
                                            .data!
                                            .single!
                                            .isNotEmpty)
                                        ? List.generate(
                                            MorningTripCubit.get()
                                                .groupAndSingleMessageModel!
                                                .data!
                                                .single!
                                                .length,
                                            (index) => PopupMenuItem(
                                                  child: Center(
                                                    child:
                                                        CustomContainerDialogW(
                                                      icons: Icons
                                                          .notifications_active,
                                                      name: MorningTripCubit
                                                              .get()
                                                          .groupAndSingleMessageModel!
                                                          .data!
                                                          .single![index]
                                                          .body!,
                                                      onTap: () {
                                                        EveningTripCubit.get(
                                                                context)
                                                            .sendMessageEveningTrip(
                                                          messageId:
                                                              MorningTripCubit
                                                                      .get()
                                                                  .groupAndSingleMessageModel!
                                                                  .data!
                                                                  .single![
                                                                      index]
                                                                  .id!,
                                                          studentId: newStudent
                                                              .students?.id,

                                                          // notificationsType: "tracking",
                                                        );
                                                        Navigator.pop(context);

                                                        // if (NotificationsCubit.get(context).parentFcmToken?.data?.isNotEmpty == true) {
                                                        //   NotificationsRepo().sendNotification(
                                                        //     deviceTokens: NotificationsCubit.get(context).parentFcmToken!.data!,
                                                        //     title: 'مساء الخير',
                                                        //     body: 'الباص تحرك من المدرسة وبه الطالب ${newStudent.students?.name}',
                                                        //   );
                                                        // }
                                                      },
                                                    ),
                                                  ),
                                                ))
                                        : [])
                                : SizedBox();

                            //  [

                            // PopupMenuItem(
                            //   child: Center(
                            //     child: CustomContainerDialogW(
                            //       icons: Icons.notifications_active,
                            //       name: 'الباص تحرك من المدرسة',
                            //       onTap: () {
                            //         EveningTripCubit.get(context).sendMessageEveningTrip(
                            //           // notificationsType: "tracking",
                            //           messageId: 8,
                            //           studentId: newStudent.students?.id,
                            //         );
                            //         Navigator.pop(context);
                            //         // if (NotificationsCubit.get(context).parentFcmToken?.data?.isNotEmpty == true) {
                            //         //   NotificationsRepo().sendNotification(
                            //         //     deviceTokens: NotificationsCubit.get(context).parentFcmToken!.data!,
                            //         //     title: 'مساء الخير',
                            //         //     body: 'الباص تحرك من المدرسة وبه الطالب ${newStudent.students?.name}',
                            //         //   );
                            //         // }
                            //       },
                            //     ),
                            //   ),
                            // ),

                            // PopupMenuItem(
                            //   child: Center(
                            //     child: CustomContainerDialogW(
                            //       icons: Icons.notification_add,
                            //       name: 'الباص بالقرب من المنزل',
                            //       onTap: () {
                            //         EveningTripCubit.get(context).sendMessageEveningTrip(
                            //           // notificationsType: "tracking",
                            //           messageId: 11,
                            //           studentId: newStudent.students?.id,
                            //         );
                            //         Navigator.pop(context);
                            //         // if (NotificationsCubit.get(context).parentFcmToken?.data?.isNotEmpty == true) {
                            //         //   NotificationsRepo().sendNotification(
                            //         //     deviceTokens: NotificationsCubit.get(context).parentFcmToken!.data!,
                            //         //     title: 'مساء الخير',
                            //         //     body: 'الطالب ${newStudent.students?.name} على وشك الوصول للمنزل',
                            //         //   );
                            //         // }
                            //       },
                            //     ),
                            //   ),
                            // ),
                            // PopupMenuItem(
                            //   child: Center(
                            //     child: CustomContainerDialogW(
                            //       icons: Icons.notification_important,
                            //       name: 'الباص وصل المنزل',
                            //       onTap: () {
                            //         EveningTripCubit.get(context).sendMessageEveningTrip(
                            //           // notificationsType: "no-tracking",
                            //           messageId: 10,
                            //           studentId: newStudent.students?.id,
                            //         );
                            //         Navigator.pop(context);
                            //         // if (NotificationsCubit.get(context).parentFcmToken?.data?.isNotEmpty == true) {
                            //         //   NotificationsRepo().sendNotification(
                            //         //     deviceTokens: NotificationsCubit.get(context).parentFcmToken!.data!,
                            //         //     title: 'مساء الخير',
                            //         //     body: 'الطالب ${newStudent.students?.name} وصل المنزل',
                            //         //   );
                            //         // }
                            //       },
                            //     ),
                            //   ),
                            // ),

                            // ],
                          },
                        ).build(context);
                      }),
                    ],
                  ),
                ),
              ),
            );
          }
        }
      },
    );
  }
}
