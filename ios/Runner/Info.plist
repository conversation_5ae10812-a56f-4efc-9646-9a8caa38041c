<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Busaty - Bus</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>bus_driver</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0.2</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Busaty needs your location while using the app to show your current position on the map and calculate bus routes.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Busaty uses your location to update bus position when the app is in background using power-efficient location updates.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Busaty uses your location to provide accurate bus tracking using power-efficient location updates, even when the app is in background.</string>
	<key>NSLocationTemporaryUsageDescriptionDictionary</key>
	<dict>
		<key>BusTracking</key>
		<string>Your precise location is needed temporarily to accurately track the bus route.</string>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>This app needs camera access to scan QR codes</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app needs photos access to get QR code from photo library</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app needs microphone access for voice features</string>
	<key>NSContactsUsageDescription</key>
	<string>This app needs contacts access to help connect you with friends</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UIRequiresPersistentWiFi</key>
	<true/>
	<key>UIIdleTimerDisabled</key>
	<true/>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>dev.flutter.background.refresh</string>
	</array>
	<key>FirebaseAppDelegateProxyEnabled</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>busaty</string>
			</array>
			<key>CFBundleURLName</key>
			<string>com.example.busaty</string>
		</dict>
	</array>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleDefault</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>CFBundleLocalizations</key>
	<array>
		<string>en</string>
		<string>ar</string>
	</array>
	<key>GoogleMapsAPIKey</key>
	<string>AIzaSyDmpwqnRpySPiDVht0Rg6sPh1ZP3cBF8fc</string>
</dict>
</plist>
